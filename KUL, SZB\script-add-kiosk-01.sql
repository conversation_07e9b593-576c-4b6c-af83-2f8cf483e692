/****** Object:  Table [dbo].[SK_KioskInformation]    Script Date: 7/3/2025 2:47:43 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SK_KioskInformation](
	[Kiosk_ID] [int] IDENTITY(1,1) NOT NULL,
	[Kiosk_Code] [varchar](20) NULL,
	[Kiosk_Model] [varchar](50) NULL,
	[Kiosk_Version] [varchar](100) NULL,
	[Kiosk_AgentVersion] [varchar](11) NULL,
	[Kiosk_DownloadAgentVersion] [varchar](11) NULL,
	[Kiosk_StatusVersion] [varchar](11) NULL,
	[Kiosk_Organization] [int] NULL,
	[Kiosk_State] [int] NULL,
	[Kiosk_Branch] [int] NULL,
	[Kiosk_Description] [varchar](50) NULL,
	[Kiosk_IP] [varchar](20) NULL,
	[Kiosk_ExtensionNumber] [varchar](4) NULL,
	[Kiosk_SerialNumber] [nvarchar](50) NULL,
	[Kiosk_ReferID] [int] NULL,
	[Kiosk_Port] [varchar](50) NULL,
	[Kiosk_Type] [varchar](50) NULL,
	[Kiosk_LastTrans] [datetime] NULL,
	[Kiosk_LastHeartBeat] [datetime] NULL,
	[Kiosk_Notify] [bit] NULL,
	[Kiosk_Active] [bit] NULL,
	[Kiosk_AllowRegister] [bit] NULL,
	[Kiosk_MacAddress] [varchar](255) NULL,
	[Kiosk_RegisteredDate] [datetime] NULL,
	[Kiosk_HDSerialNumber] [varchar](100) NULL,
	[Kiosk_DomainName] [varchar](50) NULL,
	[Kiosk_UserName] [varchar](50) NULL,
	[Kiosk_Password] [varchar](50) NULL,
	[Kiosk_CreateBy] [varchar](20) NULL,
	[Kiosk_CreatedDate] [datetime] NULL,
	[Kiosk_LastUpdateBy] [varchar](20) NULL,
	[Kiosk_LastUpdateDate] [varchar](20) NULL,
	[Kiosk_Remark] [varchar](1000) NULL,
	[Kiosk_ReportDate] [datetime] NULL,
	[Kiosk_OOSNotWithinOperationHour] [bit] NULL,
	[Kiosk_OOSUnderMaintenance] [bit] NULL,
	[Kiosk_NotifyEventID] [varchar](50) NULL,
 CONSTRAINT [PK_SK_KioskInformation] PRIMARY KEY CLUSTERED 
(
	[Kiosk_ID] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[SK_KioskInformation] ON 
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1048, N'KUL-T1-KK12-1', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', CAST(N'1900-01-01T00:00:00.000' AS DateTime), CAST(N'1900-01-01T00:00:00.000' AS DateTime), 0, 1, 1, N'', CAST(N'1900-01-01T00:00:00.000' AS DateTime), N'', N'', N'', N'', N'admin', CAST(N'2021-02-26T08:06:59.310' AS DateTime), N'admin', N'Feb 26 2021  8:06AM', N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1049, N'KUL-T1-KK12-2', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', CAST(N'1900-01-01T00:00:00.000' AS DateTime), CAST(N'1900-01-01T00:00:00.000' AS DateTime), 0, 1, 1, N'', CAST(N'1900-01-01T00:00:00.000' AS DateTime), N'', N'', N'', N'', N'admin', CAST(N'2021-02-26T08:06:59.310' AS DateTime), N'admin', N'Feb 26 2021  8:06AM', N'', NULL, 0, 0, N'')
GO
SET IDENTITY_INSERT [dbo].[SK_KioskInformation] OFF
GO
ALTER TABLE [dbo].[SK_KioskInformation] ADD  CONSTRAINT [DF_SK_KioskInformation_Kiosk_Notify]  DEFAULT ((0)) FOR [Kiosk_Notify]
GO
ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ((0)) FOR [Kiosk_OOSNotWithinOperationHour]
GO
ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ((0)) FOR [Kiosk_OOSUnderMaintenance]
GO
ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ('') FOR [Kiosk_NotifyEventID]
GO
