/****** Object:  Table [dbo].[SK_KioskInformation]    Script Date: 8/2/2025 12:22:30 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SK_KioskInformation](
	[Kiosk_ID] [int] IDENTITY(1,1) NOT NULL,
	[Kiosk_Code] [varchar](20) NULL,
	[Kiosk_Model] [varchar](50) NULL,
	[Kiosk_Version] [varchar](100) NULL,
	[Kiosk_AgentVersion] [varchar](11) NULL,
	[Kiosk_DownloadAgentVersion] [varchar](11) NULL,
	[Kiosk_StatusVersion] [varchar](11) NULL,
	[Kiosk_Organization] [int] NULL,
	[Kiosk_State] [int] NULL,
	[Kiosk_Branch] [int] NULL,
	[Kiosk_Description] [varchar](50) NULL,
	[Kiosk_IP] [varchar](20) NULL,
	[Kiosk_ExtensionNumber] [varchar](4) NULL,
	[Kiosk_SerialNumber] [nvarchar](50) NULL,
	[Kiosk_ReferID] [int] NULL,
	[Kiosk_Port] [varchar](50) NULL,
	[Kiosk_Type] [varchar](50) NULL,
	[Kiosk_LastTrans] [datetime] NULL,
	[Kiosk_LastHeartBeat] [datetime] NULL,
	[Kiosk_Notify] [bit] NULL,
	[Kiosk_Active] [bit] NULL,
	[Kiosk_AllowRegister] [bit] NULL,
	[Kiosk_MacAddress] [varchar](255) NULL,
	[Kiosk_RegisteredDate] [datetime] NULL,
	[Kiosk_HDSerialNumber] [varchar](100) NULL,
	[Kiosk_DomainName] [varchar](50) NULL,
	[Kiosk_UserName] [varchar](50) NULL,
	[Kiosk_Password] [varchar](50) NULL,
	[Kiosk_CreateBy] [varchar](20) NULL,
	[Kiosk_CreatedDate] [datetime] NULL,
	[Kiosk_LastUpdateBy] [varchar](20) NULL,
	[Kiosk_LastUpdateDate] [varchar](20) NULL,
	[Kiosk_Remark] [varchar](1000) NULL,
	[Kiosk_ReportDate] [datetime] NULL,
	[Kiosk_OOSNotWithinOperationHour] [bit] NULL,
	[Kiosk_OOSUnderMaintenance] [bit] NULL,
	[Kiosk_NotifyEventID] [varchar](50) NULL,
 CONSTRAINT [PK_SK_KioskInformation] PRIMARY KEY CLUSTERED 
(
	[Kiosk_ID] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SK_KioskInformation] ADD  CONSTRAINT [DF_SK_KioskInformation_Kiosk_Notify]  DEFAULT ((0)) FOR [Kiosk_Notify]
GO

ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ((0)) FOR [Kiosk_OOSNotWithinOperationHour]
GO

ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ((0)) FOR [Kiosk_OOSUnderMaintenance]
GO

ALTER TABLE [dbo].[SK_KioskInformation] ADD  DEFAULT ('') FOR [Kiosk_NotifyEventID]
GO


