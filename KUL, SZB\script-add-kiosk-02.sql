SET IDENTITY_INSERT [dbo].[SK_KioskInformation] ON
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1050, N'KUL-T1-KK12-3', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1051, N'KUL-T1-KK12-4', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1052, N'KUL-T1-KK12-5', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1053, N'KUL-T1-KK12-6', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1054, N'KUL-T1-KK12-7', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1055, N'KUL-T1-KK12-8', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1056, N'KUL-T1-KK12-9', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1057, N'KUL-T1-KK12-10', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1058, N'KUL-T1-KK12-11', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1059, N'KUL-T1-KK12-12', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1060, N'KUL-T1-KK12-13', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1061, N'KUL-T1-KK12-14', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1062, N'KUL-T1-KK12-15', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1063, N'KUL-T1-KK12-16', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1064, N'KUL-T1-KK12-17', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1065, N'KUL-T1-KK12-18', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1066, N'KUL-T1-KK12-19', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1067, N'KUL-T1-KK12-20', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1068, N'KUL-T1-KK12-21', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1069, N'KUL-T1-KK12-22', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1070, N'KUL-T1-KK12-23', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1071, N'KUL-T1-KK12-24', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1072, N'KUL-T1-KK12-25', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1073, N'KUL-T1-KK12-26', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1074, N'KUL-T1-KK12-27', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1075, N'KUL-T1-KK12-28', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1076, N'KUL-T1-KK12-29', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1077, N'KUL-T1-KK12-30', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1078, N'KUL-T1-KK12-31', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1079, N'KUL-T1-KK12-32', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1080, N'KUL-T1-KK12-33', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1081, N'KUL-T1-KK12-34', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1082, N'KUL-T1-KK12-35', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1083, N'KUL-T1-KK12-36', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1084, N'KUL-T1-KK12-37', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1085, N'KUL-T1-KK12-38', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1086, N'KUL-T1-KK12-39', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1087, N'KUL-T1-KK12-40', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1088, N'KUL-T1-KK12-41', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1089, N'KUL-T1-KK12-42', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1090, N'KUL-T1-KK12-43', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1091, N'KUL-T1-KK12-44', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1092, N'KUL-T1-KK12-45', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1093, N'KUL-T1-KK12-46', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1094, N'KUL-T1-KK12-47', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1095, N'KUL-T1-KK12-48', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1096, N'KUL-T1-KK12-49', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1097, N'KUL-T1-KK12-50', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1098, N'KUL-T1-KK12-51', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1099, N'KUL-T1-KK12-52', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1100, N'KUL-T1-KK12-53', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1101, N'KUL-T1-KK12-54', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1102, N'KUL-T1-KK12-55', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1103, N'KUL-T1-KK12-56', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1104, N'KUL-T1-KK12-57', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1105, N'KUL-T1-KK12-58', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1106, N'KUL-T1-KK12-59', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1107, N'KUL-T1-KK12-60', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1108, N'KUL-T1-KK12-61', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1109, N'KUL-T1-KK12-62', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1110, N'KUL-T1-KK12-63', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1111, N'KUL-T1-KK12-64', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1112, N'KUL-T1-KK12-65', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1113, N'KUL-T1-KK12-66', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1114, N'KUL-T1-KK12-67', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1115, N'KUL-T1-KK12-68', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1116, N'KUL-T1-KK12-69', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1117, N'KUL-T1-KK12-70', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1118, N'KUL-T1-KK12-71', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1119, N'KUL-T1-KK12-72', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1120, N'KUL-T1-KK12-73', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1121, N'KUL-T1-KK12-74', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1122, N'KUL-T1-KK12-75', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1123, N'KUL-T1-KK12-76', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1124, N'KUL-T1-KK12-77', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1125, N'KUL-T1-KK12-78', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1126, N'KUL-T1-KK12-79', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1127, N'KUL-T1-KK12-80', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1128, N'KUL-T1-KK12-81', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1129, N'KUL-T1-KK12-82', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1130, N'KUL-T1-KK12-83', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1131, N'KUL-T1-KK12-84', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
INSERT [dbo].[SK_KioskInformation] ([Kiosk_ID], [Kiosk_Code], [Kiosk_Model], [Kiosk_Version], [Kiosk_AgentVersion], [Kiosk_DownloadAgentVersion], [Kiosk_StatusVersion], [Kiosk_Organization], [Kiosk_State], [Kiosk_Branch], [Kiosk_Description], [Kiosk_IP], [Kiosk_ExtensionNumber], [Kiosk_SerialNumber], [Kiosk_ReferID], [Kiosk_Port], [Kiosk_Type], [Kiosk_LastTrans], [Kiosk_LastHeartBeat], [Kiosk_Notify], [Kiosk_Active], [Kiosk_AllowRegister], [Kiosk_MacAddress], [Kiosk_RegisteredDate], [Kiosk_HDSerialNumber], [Kiosk_DomainName], [Kiosk_UserName], [Kiosk_Password], [Kiosk_CreateBy], [Kiosk_CreatedDate], [Kiosk_LastUpdateBy], [Kiosk_LastUpdateDate], [Kiosk_Remark], [Kiosk_ReportDate], [Kiosk_OOSNotWithinOperationHour], [Kiosk_OOSUnderMaintenance], [Kiosk_NotifyEventID]) VALUES (1132, N'KUL-T1-KK12-85', N'', N'', N'', N'', N'', 3, 6, 1, N'', N'', NULL, N'', 0, N'', N'2', NULL, NULL, 0, 1, 1, N'', NULL, N'', N'', N'', N'', N'admin', GETDATE(), N'admin', NULL, N'', NULL, 0, 0, N'')
GO
SET IDENTITY_INSERT [dbo].[SK_KioskInformation] OFF
GO